#!/usr/bin/env python3
"""
复杂的Python Web应用示例
功能包括：用户管理、文件上传、API接口、实时聊天、数据可视化等
端口：8080
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash, send_file
from flask_socketio import SocketIO, emit, join_room, leave_room
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import json
import sqlite3
import datetime
import uuid
import threading
import time
import random
from functools import wraps

# 应用配置
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# SocketIO配置
socketio = SocketIO(app, cors_allowed_origins="*")

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static', exist_ok=True)
os.makedirs('templates', exist_ok=True)

# 数据库初始化
def init_db():
    """初始化数据库"""
    conn = sqlite3.connect('webapp.db')
    cursor = conn.cursor()

    # 用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    ''')

    # 文件表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            original_filename TEXT NOT NULL,
            user_id INTEGER,
            upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_size INTEGER,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # 聊天消息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            message TEXT NOT NULL,
            room TEXT DEFAULT 'general',
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # 系统日志表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            level TEXT NOT NULL,
            message TEXT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER,
            ip_address TEXT
        )
    ''')

    conn.commit()
    conn.close()

# 装饰器：登录验证
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 日志记录函数
def log_activity(level, message, user_id=None, ip_address=None):
    """记录系统活动"""
    conn = sqlite3.connect('webapp.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO logs (level, message, user_id, ip_address)
        VALUES (?, ?, ?, ?)
    ''', (level, message, user_id, ip_address))
    conn.commit()
    conn.close()

# 路由定义
@app.route('/')
def index():
    """主页"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']

        if not username or not email or not password:
            flash('所有字段都是必填的')
            return render_template('register.html')

        password_hash = generate_password_hash(password)

        try:
            conn = sqlite3.connect('webapp.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO users (username, email, password_hash)
                VALUES (?, ?, ?)
            ''', (username, email, password_hash))
            conn.commit()
            conn.close()

            log_activity('INFO', f'新用户注册: {username}', ip_address=request.remote_addr)
            flash('注册成功！请登录。')
            return redirect(url_for('login'))

        except sqlite3.IntegrityError:
            flash('用户名或邮箱已存在')
            return render_template('register.html')

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        conn = sqlite3.connect('webapp.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, password_hash FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()

        if user and check_password_hash(user[1], password):
            session['user_id'] = user[0]
            session['username'] = username
            log_activity('INFO', f'用户登录: {username}', user[0], request.remote_addr)
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """用户登出"""
    username = session.get('username')
    log_activity('INFO', f'用户登出: {username}', session.get('user_id'), request.remote_addr)
    session.clear()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    """用户仪表板"""
    # 获取用户统计信息
    conn = sqlite3.connect('webapp.db')
    cursor = conn.cursor()

    # 用户文件数量
    cursor.execute('SELECT COUNT(*) FROM files WHERE user_id = ?', (session['user_id'],))
    file_count = cursor.fetchone()[0]

    # 最近上传的文件
    cursor.execute('''
        SELECT original_filename, upload_time, file_size
        FROM files WHERE user_id = ?
        ORDER BY upload_time DESC LIMIT 5
    ''', (session['user_id'],))
    recent_files = cursor.fetchall()

    # 系统统计
    cursor.execute('SELECT COUNT(*) FROM users')
    total_users = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM files')
    total_files = cursor.fetchone()[0]

    conn.close()

    stats = {
        'file_count': file_count,
        'recent_files': recent_files,
        'total_users': total_users,
        'total_files': total_files
    }

    return render_template('dashboard.html', stats=stats)

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload_file():
    """文件上传"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('没有选择文件')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('没有选择文件')
            return redirect(request.url)

        if file:
            original_filename = file.filename
            filename = str(uuid.uuid4()) + '_' + secure_filename(original_filename)
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(file_path)

            file_size = os.path.getsize(file_path)

            # 保存文件信息到数据库
            conn = sqlite3.connect('webapp.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO files (filename, original_filename, user_id, file_size)
                VALUES (?, ?, ?, ?)
            ''', (filename, original_filename, session['user_id'], file_size))
            conn.commit()
            conn.close()

            log_activity('INFO', f'文件上传: {original_filename}', session['user_id'], request.remote_addr)
            flash('文件上传成功！')
            return redirect(url_for('dashboard'))

    return render_template('upload.html')

@app.route('/chat')
@login_required
def chat():
    """聊天室"""
    return render_template('chat.html')

@app.route('/api/stats')
@login_required
def api_stats():
    """API: 获取统计数据"""
    conn = sqlite3.connect('webapp.db')
    cursor = conn.cursor()

    # 每日注册用户数（最近7天）
    cursor.execute('''
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM users
        WHERE created_at >= datetime('now', '-7 days')
        GROUP BY DATE(created_at)
        ORDER BY date
    ''')
    daily_registrations = cursor.fetchall()

    # 文件上传统计
    cursor.execute('''
        SELECT DATE(upload_time) as date, COUNT(*) as count
        FROM files
        WHERE upload_time >= datetime('now', '-7 days')
        GROUP BY DATE(upload_time)
        ORDER BY date
    ''')
    daily_uploads = cursor.fetchall()

    conn.close()

    return jsonify({
        'daily_registrations': daily_registrations,
        'daily_uploads': daily_uploads,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/system-info')
@login_required
def api_system_info():
    """API: 获取系统信息"""
    import psutil
    import platform

    try:
        system_info = {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'architecture': platform.architecture()[0],
            'hostname': platform.node(),
            'cpu_count': psutil.cpu_count(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory': {
                'total': psutil.virtual_memory().total,
                'available': psutil.virtual_memory().available,
                'percent': psutil.virtual_memory().percent
            },
            'disk': {
                'total': psutil.disk_usage('/').total,
                'used': psutil.disk_usage('/').used,
                'free': psutil.disk_usage('/').free
            }
        }
    except ImportError:
        # 如果psutil不可用，返回基本信息
        system_info = {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'architecture': platform.architecture()[0],
            'hostname': platform.node(),
            'note': 'Install psutil for detailed system information'
        }

    return jsonify(system_info)

# SocketIO事件处理
@socketio.on('connect')
def on_connect():
    """用户连接"""
    if 'username' in session:
        emit('status', {'msg': f'{session["username"]} 已连接到聊天室'}, broadcast=True)
        log_activity('INFO', f'用户连接聊天室: {session["username"]}', session.get('user_id'))

@socketio.on('disconnect')
def on_disconnect():
    """用户断开连接"""
    if 'username' in session:
        emit('status', {'msg': f'{session["username"]} 已离开聊天室'}, broadcast=True)

@socketio.on('message')
def handle_message(data):
    """处理聊天消息"""
    if 'username' in session:
        username = session['username']
        message = data['message']
        room = data.get('room', 'general')

        # 保存消息到数据库
        conn = sqlite3.connect('webapp.db')
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO messages (username, message, room)
            VALUES (?, ?, ?)
        ''', (username, message, room))
        conn.commit()
        conn.close()

        # 广播消息
        emit('message', {
            'username': username,
            'message': message,
            'timestamp': datetime.datetime.now().strftime('%H:%M:%S')
        }, broadcast=True)

# 后台任务：生成模拟数据
def background_task():
    """后台任务：定期生成一些模拟数据"""
    while True:
        time.sleep(30)  # 每30秒执行一次

        # 模拟系统活动
        activities = [
            'System health check completed',
            'Database backup completed',
            'Cache cleared',
            'Log rotation completed'
        ]

        activity = random.choice(activities)
        log_activity('SYSTEM', activity)

if __name__ == '__main__':
    # 初始化数据库
    init_db()

    # 启动后台任务
    background_thread = threading.Thread(target=background_task)
    background_thread.daemon = True
    background_thread.start()

    print("=" * 60)
    print("🚀 Python Web应用启动中...")
    print(f"📍 访问地址: http://localhost:8080")
    print(f"📍 或者: http://127.0.0.1:8080")
    print("=" * 60)
    print("功能特性:")
    print("  ✅ 用户注册/登录系统")
    print("  ✅ 文件上传管理")
    print("  ✅ 实时聊天室")
    print("  ✅ 数据统计API")
    print("  ✅ 系统监控")
    print("  ✅ 活动日志")
    print("=" * 60)

    # 启动应用
    socketio.run(app, host='127.0.0.1', port=8080, debug=True)
