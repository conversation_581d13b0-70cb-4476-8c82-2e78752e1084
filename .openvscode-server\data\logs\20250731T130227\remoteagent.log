2025-07-31 13:02:27.839 [info] 




2025-07-31 13:02:27.844 [info] Extension host agent started.
2025-07-31 13:02:27.849 [info] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.openvscode-server/extensions
2025-07-31 13:02:27.876 [info] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.openvscode-server/extensions
2025-07-31 13:06:50.913 [info] [**********][83dc96cb][ManagementConnection] New connection established.
2025-07-31 13:06:50.915 [info] [**********][abc67c42][ExtensionHostConnection] New connection established.
2025-07-31 13:06:51.000 [info] [**********][abc67c42][ExtensionHostConnection] <271> Launched Extension Host Process.
2025-07-31 13:20:28.431 [error] Error: EISDIR: illegal operation on a directory, read
2025-07-31 13:23:09.011 [info] Getting Manifest... ms-python.python
2025-07-31 13:23:12.150 [info] Installing extension: ms-python.python {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:23:13.125 [error] #2: https://open-vsx.org/vscode/gallery/ms-python/vscode-pylance/latest - error GET connect ECONNREFUSED 127.0.0.1:80
2025-07-31 13:23:13.125 [error] Error while getting the latest version for the extension ms-python.vscode-pylance. connect ECONNREFUSED 127.0.0.1:80
2025-07-31 13:23:13.428 [info] Getting Manifest... ms-python.debugpy
2025-07-31 13:23:14.052 [info] Installing extension: ms-python.debugpy {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"},"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-07-31 13:23:24.555 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/ms-python.debugpy-2025.6.0-linux-x64: ms-python.debugpy
2025-07-31 13:23:24.638 [info] Renamed to /home/<USER>/.openvscode-server/extensions/ms-python.debugpy-2025.6.0-linux-x64
2025-07-31 13:23:27.189 [info] Getting Manifest... kylinideteam.kylin-cpp-pack
2025-07-31 13:23:29.668 [info] Installing extension: kylinideteam.kylin-cpp-pack {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:23:30.595 [info] Getting Manifest... kylinideteam.kylin-clangd
2025-07-31 13:23:30.889 [info] Getting Manifest... haloscript.astyle-lsp-vscode
2025-07-31 13:23:31.423 [info] Getting Manifest... redhat.vscode-yaml
2025-07-31 13:23:32.086 [info] Getting Manifest... kylinideteam.kylin-cmake-tools
2025-07-31 13:23:32.152 [info] Getting Manifest... danielpinto8zz6.c-cpp-project-generator
2025-07-31 13:23:32.537 [info] Installing extension: haloscript.astyle-lsp-vscode {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:23:33.346 [info] Installing extension: danielpinto8zz6.c-cpp-project-generator {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:23:33.975 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/haloscript.astyle-lsp-vscode-0.0.1-universal: haloscript.astyle-lsp-vscode
2025-07-31 13:23:33.993 [info] Renamed to /home/<USER>/.openvscode-server/extensions/haloscript.astyle-lsp-vscode-0.0.1-universal
2025-07-31 13:23:34.018 [info] Extension installed successfully: haloscript.astyle-lsp-vscode file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:23:34.706 [info] Getting Manifest... kylinideteam.cmake-intellisence
2025-07-31 13:23:34.986 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/danielpinto8zz6.c-cpp-project-generator-1.2.20-universal: danielpinto8zz6.c-cpp-project-generator
2025-07-31 13:23:35.009 [info] Renamed to /home/<USER>/.openvscode-server/extensions/danielpinto8zz6.c-cpp-project-generator-1.2.20-universal
2025-07-31 13:23:35.041 [info] Extension installed successfully: danielpinto8zz6.c-cpp-project-generator file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:23:35.249 [info] Getting Manifest... kylinideteam.cppdebug
2025-07-31 13:23:36.713 [info] Installing extension: kylinideteam.kylin-clangd {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"},"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-07-31 13:23:36.713 [info] Installing extension: redhat.vscode-yaml {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"},"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-07-31 13:23:36.714 [info] Installing extension: kylinideteam.kylin-cmake-tools {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"},"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-07-31 13:23:36.714 [info] Installing extension: kylinideteam.cmake-intellisence {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"},"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-07-31 13:23:36.714 [info] Installing extension: kylinideteam.cppdebug {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"},"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-07-31 13:23:37.075 [info] Getting Manifest... muhammad-sammy.csharp
2025-07-31 13:23:38.908 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/redhat.vscode-yaml-1.18.0-universal: redhat.vscode-yaml
2025-07-31 13:23:38.930 [info] Rename failed because of EACCES: permission denied, rename '/home/<USER>/.openvscode-server/extensions/.0e18b297-37be-4e40-adb9-220e7e60c3ca' -> '/home/<USER>/.openvscode-server/extensions/redhat.vscode-yaml-1.18.0-universal'. Deleted from extracted location {"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/.0e18b297-37be-4e40-adb9-220e7e60c3ca","path":"/home/<USER>/.openvscode-server/extensions/.0e18b297-37be-4e40-adb9-220e7e60c3ca","scheme":"file"}
2025-07-31 13:23:38.963 [error] Error while installing the extension redhat.vscode-yaml EACCES: permission denied, rename '/home/<USER>/.openvscode-server/extensions/.0e18b297-37be-4e40-adb9-220e7e60c3ca' -> '/home/<USER>/.openvscode-server/extensions/redhat.vscode-yaml-1.18.0-universal' file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:23:39.367 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/kylinideteam.kylin-cpp-pack-0.1.0-universal: kylinideteam.kylin-cpp-pack
2025-07-31 13:23:39.381 [info] Renamed to /home/<USER>/.openvscode-server/extensions/kylinideteam.kylin-cpp-pack-0.1.0-universal
2025-07-31 13:23:39.964 [error] Error: EACCES: permission denied, rename '/home/<USER>/.openvscode-server/extensions/.0e18b297-37be-4e40-adb9-220e7e60c3ca' -> '/home/<USER>/.openvscode-server/extensions/redhat.vscode-yaml-1.18.0-universal'
    at async Object.rename (node:internal/fs/promises:778:10)
    at async Object.eR (file:///home/<USER>/out/server-main.js:27:89391)
    at async Zh.G (file:///home/<USER>/out/server-main.js:55:25558)
    at async Zh.extractUserExtension (file:///home/<USER>/out/server-main.js:55:22028)
    at async Kc.Ab (file:///home/<USER>/out/server-main.js:55:15505)
2025-07-31 13:23:40.239 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/kylinideteam.cmake-intellisence-0.3.5-universal: kylinideteam.cmake-intellisence
2025-07-31 13:23:40.257 [info] Renamed to /home/<USER>/.openvscode-server/extensions/kylinideteam.cmake-intellisence-0.3.5-universal
2025-07-31 13:23:41.177 [info] Installing extension: muhammad-sammy.csharp {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:23:41.346 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal: ms-python.python
2025-07-31 13:23:41.752 [info] Renamed to /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal
2025-07-31 13:23:41.804 [info] Extension installed successfully: ms-python.debugpy file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:23:41.805 [info] Extension installed successfully: ms-python.python file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:23:42.252 [info] Getting Manifest... ms-dotnettools.vscode-dotnet-runtime
2025-07-31 13:23:42.807 [info] Installing extension: ms-dotnettools.vscode-dotnet-runtime {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"},"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-07-31 13:23:45.342 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.7-universal: ms-dotnettools.vscode-dotnet-runtime
2025-07-31 13:23:45.361 [info] Renamed to /home/<USER>/.openvscode-server/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.7-universal
2025-07-31 13:23:49.500 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/kylinideteam.kylin-cmake-tools-0.0.4-universal: kylinideteam.kylin-cmake-tools
2025-07-31 13:23:49.592 [info] Renamed to /home/<USER>/.openvscode-server/extensions/kylinideteam.kylin-cmake-tools-0.0.4-universal
2025-07-31 13:24:09.155 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/kylinideteam.kylin-clangd-0.4.0-linux-x64: kylinideteam.kylin-clangd
2025-07-31 13:24:09.178 [info] Renamed to /home/<USER>/.openvscode-server/extensions/kylinideteam.kylin-clangd-0.4.0-linux-x64
2025-07-31 13:24:21.721 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/kylinideteam.cppdebug-0.0.9-linux-x64: kylinideteam.cppdebug
2025-07-31 13:24:21.876 [info] Renamed to /home/<USER>/.openvscode-server/extensions/kylinideteam.cppdebug-0.0.9-linux-x64
2025-07-31 13:24:21.925 [info] Rollback: Uninstalled extension kylinideteam.kylin-cpp-pack
2025-07-31 13:24:21.929 [info] Marked extension as removed kylinideteam.kylin-cpp-pack-0.1.0-universal
2025-07-31 13:24:21.933 [info] Rollback: Uninstalled extension kylinideteam.kylin-clangd
2025-07-31 13:24:21.933 [info] Extension installed successfully: kylinideteam.cmake-intellisence file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:24:21.933 [info] Extension installed successfully: kylinideteam.kylin-cmake-tools file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:24:21.933 [info] Extension installed successfully: kylinideteam.cppdebug file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:24:21.942 [info] Marked extension as removed kylinideteam.kylin-clangd-0.4.0-linux-x64
2025-07-31 13:26:32.678 [info] [**********][83dc96cb][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-31 13:26:32.712 [info] [**********][abc67c42][ExtensionHostConnection] <271> Extension Host Process exited with code: 0, signal: null.
2025-07-31 13:26:33.015 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/muhammad-sammy.csharp-2.84.19-universal: muhammad-sammy.csharp
2025-07-31 13:26:33.233 [info] [**********][1be599bf][ExtensionHostConnection] New connection established.
2025-07-31 13:26:33.233 [info] [**********][99570b2c][ManagementConnection] New connection established.
2025-07-31 13:26:33.237 [info] [**********][1be599bf][ExtensionHostConnection] <480> Launched Extension Host Process.
2025-07-31 13:26:33.284 [info] Renamed to /home/<USER>/.openvscode-server/extensions/muhammad-sammy.csharp-2.84.19-universal
2025-07-31 13:26:33.506 [info] Extension installed successfully: ms-dotnettools.vscode-dotnet-runtime file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:26:33.506 [info] Extension installed successfully: muhammad-sammy.csharp file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:26:53.629 [info] Getting Manifest... github.vscode-pull-request-github
2025-07-31 13:26:53.630 [info] Getting Manifest... editorconfig.editorconfig
2025-07-31 13:26:53.630 [info] Getting Manifest... ms-vscode.vscode-github-issue-notebooks
2025-07-31 13:26:53.631 [info] Getting Manifest... dbaeumer.vscode-eslint
2025-07-31 13:26:56.380 [info] Installing extension: dbaeumer.vscode-eslint {"isMachineScoped":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:26:56.380 [info] Installing extension: ms-vscode.vscode-github-issue-notebooks {"isMachineScoped":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:26:56.381 [info] Installing extension: editorconfig.editorconfig {"isMachineScoped":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:26:56.381 [info] Installing extension: github.vscode-pull-request-github {"isMachineScoped":false,"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.openvscode-server/extensions/extensions.json","external":"file:///home/<USER>/.openvscode-server/extensions/extensions.json","path":"/home/<USER>/.openvscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.102.3","date":"2025-07-30T07:13:26.019Z"}}
2025-07-31 13:26:57.338 [error] #38: https://open-vsx.org/vscode/gallery/vscode/github-authentication/latest - error GET connect ECONNREFUSED 127.0.0.1:80
2025-07-31 13:26:57.338 [error] Error while getting the latest version for the extension vscode.github-authentication. connect ECONNREFUSED 127.0.0.1:80
2025-07-31 13:26:59.276 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/dbaeumer.vscode-eslint-3.0.10-universal: dbaeumer.vscode-eslint
2025-07-31 13:26:59.292 [info] Renamed to /home/<USER>/.openvscode-server/extensions/dbaeumer.vscode-eslint-3.0.10-universal
2025-07-31 13:27:00.739 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/editorconfig.editorconfig-0.17.4-universal: editorconfig.editorconfig
2025-07-31 13:27:00.786 [info] Renamed to /home/<USER>/.openvscode-server/extensions/editorconfig.editorconfig-0.17.4-universal
2025-07-31 13:27:01.520 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/ms-vscode.vscode-github-issue-notebooks-0.0.133-universal: ms-vscode.vscode-github-issue-notebooks
2025-07-31 13:27:01.538 [info] Renamed to /home/<USER>/.openvscode-server/extensions/ms-vscode.vscode-github-issue-notebooks-0.0.133-universal
2025-07-31 13:27:17.043 [info] Extracted extension to file:///home/<USER>/.openvscode-server/extensions/github.vscode-pull-request-github-0.114.3-universal: github.vscode-pull-request-github
2025-07-31 13:27:17.071 [info] Renamed to /home/<USER>/.openvscode-server/extensions/github.vscode-pull-request-github-0.114.3-universal
2025-07-31 13:27:17.109 [info] Extension installed successfully: dbaeumer.vscode-eslint file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:27:17.109 [info] Extension installed successfully: editorconfig.editorconfig file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:27:17.109 [info] Extension installed successfully: ms-vscode.vscode-github-issue-notebooks file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:27:17.109 [info] Extension installed successfully: github.vscode-pull-request-github file:///home/<USER>/.openvscode-server/extensions/extensions.json
2025-07-31 13:32:55.532 [info] [**********][99570b2c][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-31 13:32:55.582 [info] [**********][1be599bf][ExtensionHostConnection] <480> Extension Host Process exited with code: 0, signal: null.
2025-07-31 13:32:55.939 [info] [**********][75d433be][ExtensionHostConnection] New connection established.
2025-07-31 13:32:55.940 [info] [**********][84d2e947][ManagementConnection] New connection established.
2025-07-31 13:32:55.945 [info] [**********][75d433be][ExtensionHostConnection] <1792> Launched Extension Host Process.
2025-07-31 14:30:21.777 [info] [**********][0318f0e7][ManagementConnection] New connection established.
2025-07-31 14:30:21.778 [info] [**********][0f368d2b][ExtensionHostConnection] New connection established.
2025-07-31 14:30:21.785 [info] [**********][0f368d2b][ExtensionHostConnection] <11312> Launched Extension Host Process.
2025-07-31 14:30:23.839 [info] [**********][0318f0e7][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-31 14:30:23.873 [info] [**********][0f368d2b][ExtensionHostConnection] <11312> Extension Host Process exited with code: 0, signal: null.
2025-07-31 14:31:05.720 [info] [**********][6acc63ed][ManagementConnection] New connection established.
2025-07-31 14:31:05.724 [info] [**********][d5da46f1][ExtensionHostConnection] New connection established.
2025-07-31 14:31:05.728 [info] [**********][d5da46f1][ExtensionHostConnection] <11396> Launched Extension Host Process.
2025-07-31 14:31:06.861 [info] [**********][6acc63ed][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-31 14:31:07.468 [info] [**********][d5da46f1][ExtensionHostConnection] <11396> Extension Host Process exited with code: 0, signal: null.
