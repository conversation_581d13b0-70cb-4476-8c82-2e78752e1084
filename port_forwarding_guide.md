# VS Code Server 端口转发解决方案

## 问题描述
当前VS Code Server容器运行在端口3000，但是web_app.py运行在端口1111/8080，需要将容器内的端口暴露到主机。

## 解决方案

### 方案1：使用VS Code Server内置端口转发（推荐）

1. **在VS Code Server中运行Python应用**：
   ```bash
   python ./web_app.py
   ```

2. **使用VS Code Server的端口转发功能**：
   - 在VS Code Server界面中，按 `Ctrl+Shift+P` 打开命令面板
   - 输入 "Forward a Port" 并选择
   - 输入端口号：`8080`（或者`1111`如果你没有修改web_app.py）
   - VS Code Server会自动创建端口转发

3. **访问应用**：
   - VS Code Server会显示一个链接，类似：`http://localhost:3000/proxy/8080/`
   - 点击这个链接即可访问你的Python web应用

### 方案2：修改web_app.py使用8080端口

我已经修改了web_app.py，将端口从1111改为8080。这样做的好处是：
- 8080是常用的web开发端口
- 避免与其他服务冲突
- 更容易记忆

### 方案3：重新启动容器并添加端口映射

如果你想要直接访问端口，可以：

1. **停止当前容器**：
   ```bash
   docker stop f76ac6ba1131
   docker rm f76ac6ba1131
   ```

2. **重新启动容器并添加端口映射**：
   ```bash
   docker run -d \
     --name vscode-server-with-python \
     -p 3000:3000 \
     -p 1111:1111 \
     -p 8080:8080 \
     -v "$(pwd):/home/<USER>" \
     gitpod/openvscode-server
   ```

3. **访问应用**：
   - VS Code Server: http://localhost:3000
   - Python Web App: http://localhost:8080

## 推荐步骤

1. **首先尝试方案1（VS Code Server端口转发）**：
   - 在VS Code Server中运行 `python ./web_app.py`
   - 使用VS Code Server的端口转发功能
   - 这是最简单且不需要重启容器的方法

2. **如果方案1不工作，使用方案3**：
   - 重新启动容器并添加端口映射
   - 这样可以直接访问端口

## 验证步骤

1. 确保Python应用正在运行
2. 检查端口是否正确监听
3. 尝试访问web应用
4. 检查浏览器控制台是否有错误

## 常见问题

- **如果看到"连接被拒绝"**：检查Python应用是否正在运行
- **如果看到空白页面**：检查浏览器控制台的错误信息
- **如果CSS/JS不加载**：可能是路径问题，检查静态文件路径
