#!/usr/bin/env python3
"""
简化的Web应用测试
用于验证端口转发是否工作
"""

from flask import Flask, jsonify, render_template_string
import datetime
import os

app = Flask(__name__)

# 简单的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Python Web App Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .success { color: green; }
        .info { color: blue; }
        .card { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐍 Python Web应用测试</h1>
        <div class="card">
            <h2 class="success">✅ 应用运行成功！</h2>
            <p><strong>当前时间:</strong> {{ current_time }}</p>
            <p><strong>运行端口:</strong> 8080</p>
            <p><strong>Python版本:</strong> {{ python_version }}</p>
            <p><strong>工作目录:</strong> {{ working_dir }}</p>
        </div>
        
        <div class="card">
            <h3>🔗 测试链接</h3>
            <ul>
                <li><a href="/api/test">API测试</a></li>
                <li><a href="/api/status">状态检查</a></li>
                <li><a href="/health">健康检查</a></li>
            </ul>
        </div>
        
        <div class="card">
            <h3>📋 说明</h3>
            <p>如果你能看到这个页面，说明：</p>
            <ul>
                <li class="success">✅ Python Flask应用正常运行</li>
                <li class="success">✅ 端口转发配置正确</li>
                <li class="success">✅ 网络连接正常</li>
            </ul>
        </div>
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    import sys
    return render_template_string(HTML_TEMPLATE, 
                                current_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                python_version=sys.version,
                                working_dir=os.getcwd())

@app.route('/api/test')
def api_test():
    """API测试"""
    return jsonify({
        'status': 'success',
        'message': 'API工作正常',
        'timestamp': datetime.datetime.now().isoformat(),
        'port': 8080
    })

@app.route('/api/status')
def api_status():
    """状态检查"""
    return jsonify({
        'status': 'running',
        'uptime': 'unknown',
        'port': 8080,
        'python_version': '3.x',
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 简化Python Web应用启动中...")
    print(f"📍 访问地址: http://localhost:8080")
    print(f"📍 或者通过VS Code Server端口转发访问")
    print("=" * 60)
    print("功能特性:")
    print("  ✅ 基本Web界面")
    print("  ✅ API接口测试")
    print("  ✅ 健康检查")
    print("=" * 60)
    
    # 启动应用
    app.run(host='0.0.0.0', port=8080, debug=True)
