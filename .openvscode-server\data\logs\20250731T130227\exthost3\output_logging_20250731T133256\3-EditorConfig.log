Initializing document watcher...
Document watcher initialized
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
/User/settings.json: Using EditorConfig core...
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
No more open editors.
No more open editors.
No more open editors.
workspace/LICENSE.txt: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/Web应用启动指南.md: {"tabSize":3,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: Using EditorConfig core...
web_app.py: editor.action.trimTrailingWhitespace
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/web_app.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
