#!/bin/bash

# 重新启动VS Code Server容器，添加端口映射
echo "🔄 重新启动VS Code Server容器，添加端口映射..."

# 停止当前容器
echo "⏹️ 停止当前容器..."
docker stop f76ac6ba1131

# 删除当前容器
echo "🗑️ 删除当前容器..."
docker rm f76ac6ba1131

# 启动新容器，映射3000和1111端口
echo "🚀 启动新容器，映射端口3000和1111..."
docker run -d \
  --name vscode-server-with-python \
  -p 3000:3000 \
  -p 1111:1111 \
  -v "$(pwd):/home/<USER>" \
  gitpod/openvscode-server

echo "✅ 容器重新启动完成！"
echo "📍 VS Code Server: http://localhost:3000"
echo "📍 Python Web App: http://localhost:1111"
echo ""
echo "现在你可以在VS Code Server中运行 python ./web_app.py"
echo "然后访问 http://localhost:1111 查看web应用"
