2025-07-31 14:31:06.431 [info] Starting Environment refresh
2025-07-31 14:31:06.432 [info] Searching for interpreters in posix paths locator
2025-07-31 14:31:06.432 [info] Searching for pyenv environments
2025-07-31 14:31:06.432 [info] Searching for conda environments
2025-07-31 14:31:06.432 [info] Searching for global virtual environments
2025-07-31 14:31:06.432 [info] Searching for custom virtual environments
2025-07-31 14:31:06.432 [info] pyenv is not installed
2025-07-31 14:31:06.432 [info] Finished searching for pyenv environments: 8 milliseconds
2025-07-31 14:31:06.432 [info] Finished searching for custom virtual envs: 7 milliseconds
2025-07-31 14:31:06.434 [info] > conda info --json
2025-07-31 14:31:06.434 [info] Finished searching for global virtual envs: 11 milliseconds
2025-07-31 14:31:06.434 [info] Found: /bin/python --> /usr/bin/python3.10
2025-07-31 14:31:06.434 [info] Found: /bin/python3 --> /bin/python3.10
2025-07-31 14:31:06.434 [info] Found: /bin/python3.10 --> /bin/python3.10
2025-07-31 14:31:06.476 [info] Found: /usr/bin/python --> /usr/bin/python3.10
2025-07-31 14:31:06.477 [info] Found: /usr/bin/python3 --> /usr/bin/python3.10
2025-07-31 14:31:06.479 [info] > /bin/python3 -I ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 14:31:06.479 [info] Found: /usr/bin/python3.10 --> /usr/bin/python3.10
2025-07-31 14:31:07.434 [info] Python interpreter path: /bin/python3
2025-07-31 14:31:07.451 [info] Finished searching for interpreters in posix paths locator: 1033 milliseconds
2025-07-31 14:31:07.451 [info] > /bin/python -I ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
