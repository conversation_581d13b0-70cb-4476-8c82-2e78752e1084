2025-07-31 14:30:22.561 [info] Starting Environment refresh
2025-07-31 14:30:22.562 [info] Searching for interpreters in posix paths locator
2025-07-31 14:30:22.562 [info] Searching for pyenv environments
2025-07-31 14:30:22.562 [info] Searching for conda environments
2025-07-31 14:30:22.562 [info] Searching for global virtual environments
2025-07-31 14:30:22.562 [info] Searching for custom virtual environments
2025-07-31 14:30:22.562 [info] pyenv is not installed
2025-07-31 14:30:22.562 [info] Finished searching for pyenv environments: 10 milliseconds
2025-07-31 14:30:22.562 [info] Finished searching for custom virtual envs: 10 milliseconds
2025-07-31 14:30:22.569 [info] > conda info --json
2025-07-31 14:30:22.569 [info] Finished searching for global virtual envs: 14 milliseconds
2025-07-31 14:30:22.569 [info] Found: /bin/python --> /usr/bin/python3.10
2025-07-31 14:30:22.569 [info] Found: /bin/python3 --> /bin/python3.10
2025-07-31 14:30:22.570 [info] Found: /bin/python3.10 --> /bin/python3.10
2025-07-31 14:30:22.625 [info] > /bin/python3 -I ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 14:30:22.635 [info] Found: /usr/bin/python --> /usr/bin/python3.10
2025-07-31 14:30:23.675 [info] Found: /usr/bin/python3 --> /usr/bin/python3.10
2025-07-31 14:30:23.678 [info] Python interpreter path: /bin/python3
2025-07-31 14:30:23.679 [info] Found: /usr/bin/python3.10 --> /usr/bin/python3.10
