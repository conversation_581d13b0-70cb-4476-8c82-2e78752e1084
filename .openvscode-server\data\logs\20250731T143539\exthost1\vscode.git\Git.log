2025-07-31 14:35:49.741 [info] [main] Log level: Info
2025-07-31 14:35:49.741 [info] [main] Validating found git in: "git"
2025-07-31 14:35:49.741 [info] [main] Using git "2.34.1" from "git"
2025-07-31 14:35:49.741 [info] [Model][doInitialScan] Initial repository scan started
2025-07-31 14:35:49.741 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-07-31 14:35:50.738 [info] > git rev-parse --show-toplevel [11ms]
2025-07-31 14:35:50.738 [info] fatal: not a git repository (or any parent up to mount point /home)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
