2025-07-31 14:35:48.461 [info] Starting Environment refresh
2025-07-31 14:35:48.461 [info] Searching for interpreters in posix paths locator
2025-07-31 14:35:48.461 [info] Searching for pyenv environments
2025-07-31 14:35:48.462 [info] Searching for conda environments
2025-07-31 14:35:48.462 [info] Searching for global virtual environments
2025-07-31 14:35:48.462 [info] Searching for custom virtual environments
2025-07-31 14:35:48.462 [info] pyenv is not installed
2025-07-31 14:35:48.462 [info] Finished searching for pyenv environments: 10 milliseconds
2025-07-31 14:35:48.462 [info] Finished searching for custom virtual envs: 10 milliseconds
2025-07-31 14:35:48.464 [info] > conda info --json
2025-07-31 14:35:48.464 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 14:35:48.464 [info] Finished searching for global virtual envs: 14 milliseconds
2025-07-31 14:35:48.510 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 14:35:48.510 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 14:35:48.522 [info] > /bin/python3 -I ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 14:35:48.525 [error] [Error: Command failed: /bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
/bin/sh: 1: /bin/python3: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: '/bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py'
}
2025-07-31 14:35:49.605 [info] Finished searching for interpreters in posix paths locator: 1164 milliseconds
2025-07-31 14:35:49.605 [info] Environments refresh paths discovered (event): 1164 milliseconds
2025-07-31 14:35:49.606 [info] Environments refresh finished (event): 1164 milliseconds
2025-07-31 14:35:49.606 [info] Environments refresh paths discovered: 1165 milliseconds
2025-07-31 14:35:49.607 [info] Environment refresh took 1167 milliseconds
2025-07-31 14:35:50.527 [info] > /bin/python3 -I ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ~/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 14:35:50.529 [error] [Error: Command failed: /bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
/bin/sh: 1: /bin/python3: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at ChildProcess._handle.onexit (node:internal/child_process:304:5)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: '/bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py'
}
2025-07-31 14:35:50.530 [error] Unable to start Jedi language server as a valid interpreter is not selected
2025-07-31 14:35:50.532 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 14:35:50.532 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 14:35:50.533 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 14:35:50.727 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
