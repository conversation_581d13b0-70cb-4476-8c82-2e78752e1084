2025-07-31 13:32:56.821 [warning] Dir "/home/<USER>/.pixi/envs" is not watchable (directory does not exist)
2025-07-31 13:32:56.822 [info] Starting Environment refresh
2025-07-31 13:32:56.822 [info] Searching for interpreters in posix paths locator
2025-07-31 13:32:56.822 [info] Searching for pyenv environments
2025-07-31 13:32:56.822 [info] Searching for conda environments
2025-07-31 13:32:56.822 [info] Searching for global virtual environments
2025-07-31 13:32:56.822 [info] Searching for custom virtual environments
2025-07-31 13:32:56.823 [info] pyenv is not installed
2025-07-31 13:32:56.823 [info] Finished searching for pyenv environments: 53 milliseconds
2025-07-31 13:32:56.824 [info] Finished searching for custom virtual envs: 52 milliseconds
2025-07-31 13:32:56.824 [info] > conda info --json
2025-07-31 13:32:56.824 [info] > hatch env show --json
2025-07-31 13:32:56.824 [info] cwd: .
2025-07-31 13:32:56.824 [info] Finished searching for global virtual envs: 58 milliseconds
2025-07-31 13:32:56.863 [info] > C:\Python313\python.exe -c "import sys;print(sys.executable)"
2025-07-31 13:32:56.865 [error] [Error: Command failed: C:\Python313\python.exe -c "import sys;print(sys.executable)"
/bin/sh: 1: C:Python313python.exe: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: 'C:\\Python313\\python.exe -c "import sys;print(sys.executable)"'
}
2025-07-31 13:32:56.868 [info] Finished searching for interpreters in posix paths locator: 111 milliseconds
2025-07-31 13:32:56.871 [info] > C:\Python313\python.exe -I ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 13:32:56.898 [error] [Error: Command failed: C:\Python313\python.exe -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
/bin/sh: 1: C:Python313python.exe: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: 'C:\\Python313\\python.exe -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py'
}
2025-07-31 13:32:57.684 [info] Environments refresh paths discovered (event): 926 milliseconds
2025-07-31 13:32:57.685 [info] Environments refresh paths discovered: 927 milliseconds
2025-07-31 13:32:59.388 [info] > C:\Python313\python.exe -I ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 13:32:59.389 [error] [Error: Command failed: C:\Python313\python.exe -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
/bin/sh: 1: C:Python313python.exe: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: 'C:\\Python313\\python.exe -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py'
}
2025-07-31 13:32:59.390 [info] Active interpreter [/home/<USER>
2025-07-31 13:32:59.390 [info] Environments refresh finished (event): 2633 milliseconds
2025-07-31 13:32:59.391 [info] Environment refresh took 2635 milliseconds
2025-07-31 13:32:59.392 [info] > pyenv which python
2025-07-31 13:32:59.392 [info] cwd: .
2025-07-31 13:32:59.540 [error] Unable to start Jedi language server as a valid interpreter is not selected
2025-07-31 13:33:08.348 [info] Starting Environment refresh
2025-07-31 13:33:08.348 [info] Searching for interpreters in posix paths locator
2025-07-31 13:33:08.349 [info] Searching for pyenv environments
2025-07-31 13:33:08.349 [info] Searching for conda environments
2025-07-31 13:33:08.349 [info] Searching for global virtual environments
2025-07-31 13:33:08.349 [info] Searching for custom virtual environments
2025-07-31 13:33:08.350 [info] pyenv is not installed
2025-07-31 13:33:08.350 [info] Finished searching for pyenv environments: 2 milliseconds
2025-07-31 13:33:08.351 [info] Finished searching for custom virtual envs: 1 milliseconds
2025-07-31 13:33:08.351 [info] Finished searching for global virtual envs: 2 milliseconds
2025-07-31 13:33:08.354 [info] Finished searching for interpreters in posix paths locator: 6 milliseconds
2025-07-31 13:33:08.589 [info] Environments refresh paths discovered (event): 240 milliseconds
2025-07-31 13:33:08.589 [info] Environments refresh finished (event): 240 milliseconds
2025-07-31 13:33:08.589 [info] Environments refresh paths discovered: 241 milliseconds
2025-07-31 13:33:08.590 [info] Environment refresh took 242 milliseconds
2025-07-31 13:33:10.517 [warning] Failed to check if C:\Python313\python.exe is an executable [Error: ENOENT: no such file or directory, lstat 'C:\Python313\python.exe'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: 'C:\\Python313\\python.exe'
}
2025-07-31 13:33:12.627 [info] Starting Environment refresh
2025-07-31 13:33:12.627 [info] Searching for interpreters in posix paths locator
2025-07-31 13:33:12.628 [info] Searching for pyenv environments
2025-07-31 13:33:12.628 [info] Searching for conda environments
2025-07-31 13:33:12.628 [info] Searching for global virtual environments
2025-07-31 13:33:12.628 [info] Searching for custom virtual environments
2025-07-31 13:33:12.630 [info] Finished searching for custom virtual envs: 2 milliseconds
2025-07-31 13:33:12.630 [info] pyenv is not installed
2025-07-31 13:33:12.630 [info] Finished searching for pyenv environments: 2 milliseconds
2025-07-31 13:33:12.631 [info] Finished searching for global virtual envs: 3 milliseconds
2025-07-31 13:33:12.636 [info] Finished searching for interpreters in posix paths locator: 9 milliseconds
2025-07-31 13:33:12.789 [info] Environments refresh paths discovered (event): 163 milliseconds
2025-07-31 13:33:12.790 [info] Environments refresh finished (event): 163 milliseconds
2025-07-31 13:33:12.790 [info] Environments refresh paths discovered: 164 milliseconds
2025-07-31 13:33:12.790 [info] Environment refresh took 164 milliseconds
2025-07-31 13:33:17.210 [warning] Failed to check if C:\Python313\python.exe is an executable [Error: ENOENT: no such file or directory, lstat 'C:\Python313\python.exe'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: 'C:\\Python313\\python.exe'
}
2025-07-31 13:33:51.053 [info] Discover tests for workspace name: undefined - uri: /User/settings.json
2025-07-31 14:27:56.969 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:01.582 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:03.907 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:08.014 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:10.714 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:14.934 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:32.377 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:33.673 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:37.821 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:44.461 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:28:48.745 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:31:52.877 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
2025-07-31 14:31:55.977 [info] Discover tests for workspace name: workspace - uri: /home/<USER>/web_app.py
