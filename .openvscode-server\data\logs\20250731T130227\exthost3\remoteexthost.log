2025-07-31 13:32:56.497 [info] Extension host with pid 1792 started
2025-07-31 13:32:56.513 [info] Lock '/home/<USER>/.openvscode-server/data/User/workspaceStorage/12fe0316/vscode.lock': Lock acquired.
2025-07-31 13:32:56.556 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-31 13:32:56.557 [info] ExtensionService#_doActivateExtension ms-python.python, startup: false, activationEvent: 'onLanguage:python', root cause: ms-python.debugpy
2025-07-31 13:32:56.762 [info] ExtensionService#_doActivateExtension ms-python.debugpy, startup: false, activationEvent: 'onLanguage:python'
2025-07-31 13:32:56.834 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-31 13:32:56.837 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-07-31 13:32:56.854 [info] ExtensionService#_doActivateExtension ms-vscode.vscode-selfhost-test-provider, startup: true, activationEvent: 'workspaceContains:src/vs/loader.js'
2025-07-31 13:32:56.860 [error] Activating extension ms-vscode.vscode-selfhost-test-provider failed due to an error:
2025-07-31 13:32:56.860 [error] Error: Cannot find module '/home/<USER>/.vscode/extensions/vscode-selfhost-test-provider/out/extension.js'
Require stack:
- /home/<USER>/out/vs/workbench/api/node/extensionHostProcess.js
	at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
	at t._resolveFilename (file:///home/<USER>/out/vs/workbench/api/node/extensionHostProcess.js:174:22979)
	at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
	at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
	at Function.<anonymous> (node:internal/modules/cjs/loader:1211:37)
	at e._load (file:///home/<USER>/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///home/<USER>/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///home/<USER>/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
	at Module.require (node:internal/modules/cjs/loader:1487:12)
	at require (node:internal/modules/helpers:135:16)
	at QG.Cb (file:///home/<USER>/out/vs/workbench/api/node/extensionHostProcess.js:205:1253)
2025-07-31 13:32:56.872 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-31 13:32:56.872 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-31 13:32:58.508 [info] Eager extensions activated
2025-07-31 13:32:58.509 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 13:32:58.509 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 13:32:58.509 [info] ExtensionService#_doActivateExtension dbaeumer.vscode-eslint, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 13:32:58.510 [info] ExtensionService#_doActivateExtension EditorConfig.EditorConfig, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 13:32:58.510 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onStartupFinished', root cause: GitHub.vscode-pull-request-github
2025-07-31 13:32:58.510 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 13:32:59.418 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 13:33:02.476 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-07-31 13:33:02.477 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-07-31 13:33:02.477 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-07-31 13:33:50.712 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-07-31 13:38:56.673 [info] ExtensionService#_doActivateExtension vscode.terminal-suggest, startup: false, activationEvent: 'onTerminalCompletionsRequested'
2025-07-31 13:59:58.972 [info] ExtensionService#_doActivateExtension vscode.simple-browser, startup: false, activationEvent: 'onOpenExternalUri:http'
2025-07-31 14:02:25.019 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-07-31 14:02:25.262 [info] ExtensionService#_doActivateExtension vscode.markdown-math, startup: false, activationEvent: 'api', root cause: vscode.markdown-language-features
